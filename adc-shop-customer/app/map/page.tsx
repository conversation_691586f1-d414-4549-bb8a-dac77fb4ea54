'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, MapPin, Navigation } from 'lucide-react';
import ShopLocator from '@/components/ShopLocator';
import MapTest from '@/components/MapTest';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ShopLocation, openDirections } from '@/lib/google-maps';
import { generateShopUrl, generateBranchUrl, getDefaultShopType } from '@/lib/config/shop-types';

export default function MapPage() {
  const router = useRouter();
  const [selectedShop, setSelectedShop] = useState<ShopLocation | null>(null);

  const handleShopSelect = (shop: ShopLocation) => {
    setSelectedShop(shop);
  };

  const handleViewShop = () => {
    if (selectedShop) {
      // Default to 'food' shop type for now
      const shopType = getDefaultShopType();

      // If this is a branch location, navigate to the branch-specific URL
      if (selectedShop.shopSlug && selectedShop.branchSlug) {
        const url = generateBranchUrl(shopType, selectedShop.shopSlug, selectedShop.branchSlug);
        router.push(url);
      } else {
        // Fallback to shop URL for shops without branches
        const url = generateShopUrl(shopType, selectedShop.slug);
        router.push(url);
      }
    }
  };

  const handleGetDirections = () => {
    if (selectedShop) {
      // Use the smart directions opener that detects device type
      openDirections(
        { lat: selectedShop.lat, lng: selectedShop.lng },
        selectedShop.name
      );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Restaurant Map</h1>
                <p className="text-sm text-gray-600">Find restaurants near you</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <MapPin className="w-5 h-5 text-orange-500" />
              <span className="text-sm font-medium text-gray-700">Interactive Map</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="space-y-6">
          {/* API Test Component */}
          <div className="flex justify-center">
            <MapTest />
          </div>
          {/* Selected Shop Info */}
          {selectedShop && (
            <Card className="bg-orange-50 border-orange-200">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="text-orange-800">Selected Restaurant</span>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleGetDirections}
                      className="flex items-center gap-2"
                    >
                      <Navigation className="w-4 h-4" />
                      Directions
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleViewShop}
                      className="bg-orange-500 hover:bg-orange-600"
                    >
                      View Menu
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold text-lg text-orange-800 mb-1">
                      {selectedShop.name}
                    </h3>
                    <p className="text-orange-700 mb-2">{selectedShop.address}</p>
                    <div className="flex items-center gap-4 text-sm">
                      {selectedShop.rating && (
                        <div className="flex items-center gap-1">
                          <span className="text-yellow-500">★</span>
                          <span className="text-orange-700">{selectedShop.rating.toFixed(1)}</span>
                        </div>
                      )}
                      {selectedShop.cuisineType && (
                        <span className="bg-orange-200 text-orange-800 px-2 py-1 rounded text-xs">
                          {selectedShop.cuisineType}
                        </span>
                      )}
                      {selectedShop.priceRange && (
                        <span className="text-orange-700">{selectedShop.priceRange}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-end">
                    {selectedShop.distance && (
                      <div className="text-right">
                        <p className="text-sm text-orange-600">Distance</p>
                        <p className="text-lg font-semibold text-orange-800">
                          {selectedShop.distance.toFixed(1)}km
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Shop Locator */}
          <ShopLocator onShopSelect={handleShopSelect} />

          {/* Instructions */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-800 flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                How to Use the Map
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                <div>
                  <h4 className="font-semibold mb-2">🔍 Search & Filter</h4>
                  <ul className="space-y-1">
                    <li>• Search by restaurant name or address</li>
                    <li>• Filter by cuisine type and price range</li>
                    <li>• Sort by distance, rating, or name</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">🗺️ Map Interaction</h4>
                  <ul className="space-y-1">
                    <li>• Click on map markers for restaurant details</li>
                    <li>• Use "My Location" to find nearby restaurants</li>
                    <li>• Click on restaurant cards to select them</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <MapPin className="w-6 h-6 text-orange-600" />
                </div>
                <h3 className="font-semibold mb-2">Real-time Location</h3>
                <p className="text-sm text-gray-600">
                  Find restaurants based on your current location with accurate distance calculations.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Navigation className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Turn-by-turn Directions</h3>
                <p className="text-sm text-gray-600">
                  Get directions to any restaurant directly through Google Maps integration.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold mb-2">Smart Search</h3>
                <p className="text-sm text-gray-600">
                  Search by name, cuisine type, or address with intelligent filtering options.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
